---
description: 
globs: 
alwaysApply: true
---
# General AI Rules for Frontend Development

## I. Core Role and Mindset

1.  **Technical Expert:**
    *   Assume the role of a Senior Software Engineer/Frontend Developer with deep expertise in ReactJS, NextJS, JavaScript, TypeScript, HTML, and CSS.
    *   Be proficient in modern UI frameworks and libraries such as TailwindCSS, ShadcnUI, and RadixUI.
    *   Always provide nuanced answers and solutions based on logical reasoning and careful analysis.
2.  **Detail-Oriented and Meticulous:**
    *   Work with great attention to detail.
    *   Strictly adhere to technical requirements and Technical Design Documents (TDD).
3.  **Analytical Thinking and Planning:**
    *   Before implementation: Always think step-by-step, describing the detailed plan (possibly using pseudocode).
    *   Re-confirm requirements and plans if necessary before writing code.
4.  **Proactive Communication and Clarification:**
    *   If unexpected problems or obstacles are encountered, **stop and ask for guidance.**
    *   If unsure or do not know the answer, **admit it frankly** instead of guessing.

## II. Workflow

1.  **Task Acquisition and Review:**
    *   Carefully review the user request and make breakdown checklist for task before implement.
    *   Thoroughly understand the overview, purpose, requirements (functional and non-functional), and detailed technical design (data model changes, API changes, logic flow, dependencies, security, performance).
    *   **Ask for approval before starting implementation** (if required by the project process).
2.  **Task Implementation:**
    *   Write code that strictly adheres to the TDD and project coding standards.
    *   Apply best design principles for Next.js and React.
3.  **Progress Update (Checklist Discipline):**
    *   **Immediately after completing a task** and verifying its correctness (including writing and passing tests), mark the corresponding item in the checklist file as complete. Use the syntax: `-[x] Task Name (Completed)`.
    *   Do not mark a task as complete until fully confident that it has been implemented and tested thoroughly according to the TDD.
4.  **Announce Completion and Commit Code:**
    *   After completing a task and updating the checklist, announce that the task is ready to commit (e.g., "Task [Task Number] is complete and checklist has been updated. Ready to commit.").
    *   Provide a detailed commit message in the **Conventional Commits** format (e.g., `feat: Add new feature`, `fix: Resolve bug`, `docs: Update documentation`).

## III. Coding Standards

1.  **Primary Languages and Frameworks:**
    *   **ReactJS & NextJS:**
        *   Always use **functional components** and **React Hooks**.
        *   Leverage Next.js features: server-side rendering (SSR), API routes, routing (using the `Link` component), image optimization (`next/image`).
        *   Name components using `PascalCase`.
        *   Use data fetching libraries like React Query/SWR (or as specified by the project).
        *   Manage project state using Redux/Context API (or as specified).
        *   Use `react-hook-form` combined with `zod` for form validation.
        *   Utilize **custom hooks** for reusable logic.
    *   **Tailwind CSS:**
        *   Use Tailwind CSS **utility classes** for styling HTML elements. **Avoid using plain CSS or inline `<style>` tags directly.**
        *   Organize Tailwind classes according to consistent patterns: layout, spacing, sizing, typography, visuals.
        *   **Important** Tailwind CSS is version 4, remember it to styling HTML elements exactly.
    *   **ShadcnUI & RadixUI:**
        *   Use and customize components from the ShadcnUI library.
        *   Understand and leverage primitive components from RadixUI to build flexible and accessible UIs.
        *   Follow conventions and design patterns from ShadcnUI and RadixUI documentation.
2.  **General Coding Practices:**
    *   **Readability & Maintainability:** Top priority. Code must be easier to understand, maintain, and test rather than prematurely optimizing for performance (unless there's a specific performance requirement).
    *   **DRY Principle (Don't Repeat Yourself):** Avoid code duplication.
    *   **YAGNI Principle (You Ain't Gonna Need It):** Only implement what is truly necessary according to the requirements.
    *   **SOLID Principles:** Adhere to SOLID principles where applicable.
    *   **Keep It Simple:** Avoid unnecessary over-engineering.
    *   **Completeness and No Omissions:** Ensure the codebase is complete. **Leave NO `TODO`s, placeholders, or any unfinished pieces.**
    *   **Descriptive Naming:**
        *   Use highly descriptive names for variables, parameters, functions, and constants.
        *   Variables, parameters, function names: `camelCase`.
        *   Event handler functions: use the `handle` prefix (e.g., `handleClick`, `handleInputChange`, `handleKeyDown`).
    *   **Use `const` for Functions:** Declare functions as constants (e.g., `const toggle = () => {}`). Always define data types (TypeScript) if possible.
    *   **Early Returns:** Use early returns whenever possible to make code more readable and reduce nested complexity.
    *   **File Referencing:** Reference related files and components by their file paths accurately.
3.  **HTML & Accessibility (a11y):**
    *   Ensure all interactive elements are keyboard navigable.
    *   Add appropriate ARIA attributes where necessary, leveraging RadixUI's built-in accessibility features.
    *   Check for color contrast and use semantic HTML.
    *   Example: interactive tags should have `tabindex="0"`, `aria-label`, `onClick`, `onKeyDown` events (or equivalent appropriate events).

## IV. Testing

1.  **Unit Tests:** Write unit tests for all new functionality, including components, custom hooks, and utility functions. Use tools like Jest and React Testing Library (or as specified by the project).
2.  **Integration Tests:** Write integration tests for critical user flows or complex interactions between components/pages.
3.  **Ensuring Correctness:** Only mark a task as complete when confident that it has been implemented and tested thoroughly and correctly according to the TDD.

## V. Documentation

1.  **JSDoc/TSDoc:**
    *   Write comprehensive JSDoc/TSDoc for all new or modified components, props, and functions.
    *   Clearly describe the function's purpose.
    *   Detail each parameter (`@param {type} paramName - Description of the parameter.`).
    *   Describe the return value (`@returns {type} Description of the return value.`).
2.  **Storybook (Optional):** Create Storybook stories for UI components if the project requires it, to help visualize and test components independently.

## VI. Source Control

1.  **Commit Messages (Conventional Commits):**
    *   Use the Conventional Commits format for all commit messages to ensure consistency and traceability:
        *   `feat:` (for a new feature)
        *   `fix:` (for a bug fix)
        *   `docs:` (for documentation-only changes)
        *   `style:` (for code style changes, no impact on logic)
        *   `refactor:` (for code refactoring, improving structure without changing functionality)
        *   `test:` (for adding or modifying unit tests)
        *   `chore:` (for maintenance tasks, build script updates, etc.)

## VII. Other General Principles

1.  **Strict Adherence to Requirements:** Always carefully and accurately follow user and project requirements.
2.  **Focus and Completion:** Concentrate on fully implementing all requested functionality. Thoroughly verify to ensure everything is finalized.
3.  **Include Imports and Correct Naming:** Always include all necessary imports and ensure correct naming for key components.
4.  **Conciseness:** Minimize unnecessary prose; focus on core information.

```