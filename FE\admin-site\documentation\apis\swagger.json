{"openapi": "3.0.1", "info": {"title": "DecorStore API", "version": "v1"}, "paths": {"/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResponseDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDTO"}}}}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResponseDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDTO"}}}}}}}, "/api/Auth/user": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDTO"}}}}}}}, "/api/Auth/check-claims": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/make-admin": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDTO"}}}}}}}, "/api/Banner": {"get": {"tags": ["Banner"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BannerDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BannerDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BannerDTO"}}}}}}}, "post": {"tags": ["Banner"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["ImageFile", "Title"], "type": "object", "properties": {"Title": {"maxLength": 100, "minLength": 0, "type": "string"}, "ImageFile": {"type": "string", "format": "binary"}, "Link": {"maxLength": 255, "minLength": 0, "type": "string"}, "IsActive": {"type": "boolean"}, "DisplayOrder": {"type": "integer", "format": "int32"}}}, "encoding": {"Title": {"style": "form"}, "ImageFile": {"style": "form"}, "Link": {"style": "form"}, "IsActive": {"style": "form"}, "DisplayOrder": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Banner"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Banner"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Banner"}}}}}}}, "/api/Banner/active": {"get": {"tags": ["Banner"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BannerDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BannerDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BannerDTO"}}}}}}}}, "/api/Banner/{id}": {"get": {"tags": ["Banner"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BannerDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BannerDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BannerDTO"}}}}}}, "put": {"tags": ["Banner"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Title"], "type": "object", "properties": {"Title": {"maxLength": 100, "minLength": 0, "type": "string"}, "ImageFile": {"type": "string", "format": "binary"}, "Link": {"maxLength": 255, "minLength": 0, "type": "string"}, "IsActive": {"type": "boolean"}, "DisplayOrder": {"type": "integer", "format": "int32"}}}, "encoding": {"Title": {"style": "form"}, "ImageFile": {"style": "form"}, "Link": {"style": "form"}, "IsActive": {"style": "form"}, "DisplayOrder": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Banner"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Cart": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDTO"}}}}}}, "post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddToCartDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddToCartDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddToCartDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDTO"}}}}}}, "delete": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDTO"}}}}}}}, "/api/Cart/items/{id}": {"put": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCartItemDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCartItemDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCartItemDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDTO"}}}}}}, "delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDTO"}}}}}}}, "/api/Cart/merge": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Category": {"get": {"tags": ["Category"], "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "ParentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsRootCategory", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeSubcategories", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeProductCount", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreatedAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "IncludeDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryDTOPagedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryDTOPagedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryDTOPagedResult"}}}}}}, "post": {"tags": ["Category"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Category"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Category"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Category"}}}}}}}, "/api/Category/all": {"get": {"tags": ["Category"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}}}}}}, "/api/Category/hierarchical": {"get": {"tags": ["Category"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}}}}}}, "/api/Category/{id}": {"get": {"tags": ["Category"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryDTO"}}}}}}, "put": {"tags": ["Category"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Category"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/slug/{slug}": {"get": {"tags": ["Category"], "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryDTO"}}}}}}}, "/api/Category/with-product-count": {"get": {"tags": ["Category"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}}}}}}, "/api/Category/{parentId}/subcategories": {"get": {"tags": ["Category"], "parameters": [{"name": "parentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}}}}}}, "/api/Category/{categoryId}/product-count": {"get": {"tags": ["Category"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/Category/popular": {"get": {"tags": ["Category"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}}}}}}, "/api/Category/root": {"get": {"tags": ["Category"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}}}}}}}, "/api/Category/import": {"post": {"tags": ["Category"], "parameters": [{"name": "validateOnly", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryExcelDTOExcelImportResultDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryExcelDTOExcelImportResultDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryExcelDTOExcelImportResultDTO"}}}}}}}, "/api/Category/export": {"get": {"tags": ["Category"], "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "ParentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsRootCategory", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeSubcategories", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeProductCount", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreatedAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "IncludeDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "format", "in": "query", "schema": {"type": "string", "default": "xlsx"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/export-template": {"get": {"tags": ["Category"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category/validate-import": {"post": {"tags": ["Category"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}}}}}}, "/api/Category/import-statistics": {"post": {"tags": ["Category"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryImportStatisticsDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryImportStatisticsDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryImportStatisticsDTO"}}}}}}}, "/api/Customer": {"get": {"tags": ["Customer"], "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}, {"name": "City", "in": "query", "schema": {"type": "string"}}, {"name": "State", "in": "query", "schema": {"type": "string"}}, {"name": "Country", "in": "query", "schema": {"type": "string"}}, {"name": "PostalCode", "in": "query", "schema": {"type": "string"}}, {"name": "RegisteredAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "RegisteredBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "HasOrders", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeOrderCount", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeTotalSpent", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CustomerDTOPagedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CustomerDTOPagedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerDTOPagedResult"}}}}}}, "post": {"tags": ["Customer"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCustomerDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCustomerDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCustomerDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Customer"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Customer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Customer"}}}}}}}, "/api/Customer/all": {"get": {"tags": ["Customer"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}}}}}}, "/api/Customer/{id}": {"get": {"tags": ["Customer"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CustomerDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CustomerDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerDTO"}}}}}}, "put": {"tags": ["Customer"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomerDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomerDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCustomerDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Customer"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Customer/email/{email}": {"get": {"tags": ["Customer"], "parameters": [{"name": "email", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CustomerDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CustomerDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerDTO"}}}}}}}, "/api/Customer/with-orders": {"get": {"tags": ["Customer"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}}}}}}, "/api/Customer/top-by-order-count": {"get": {"tags": ["Customer"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}}}}}}, "/api/Customer/top-by-spending": {"get": {"tags": ["Customer"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}}}}}}, "/api/Customer/{customerId}/order-count": {"get": {"tags": ["Customer"], "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/Customer/{customerId}/total-spent": {"get": {"tags": ["Customer"], "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "number", "format": "double"}}, "application/json": {"schema": {"type": "number", "format": "double"}}, "text/json": {"schema": {"type": "number", "format": "double"}}}}}}}, "/api/Customer/by-location": {"get": {"tags": ["Customer"], "parameters": [{"name": "city", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}, {"name": "country", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}}}}}}}}, "/api/Customer/import": {"post": {"tags": ["Customer"], "parameters": [{"name": "validateOnly", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CustomerExcelDTOExcelImportResultDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CustomerExcelDTOExcelImportResultDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerExcelDTOExcelImportResultDTO"}}}}}}}, "/api/Customer/export": {"get": {"tags": ["Customer"], "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}, {"name": "City", "in": "query", "schema": {"type": "string"}}, {"name": "State", "in": "query", "schema": {"type": "string"}}, {"name": "Country", "in": "query", "schema": {"type": "string"}}, {"name": "PostalCode", "in": "query", "schema": {"type": "string"}}, {"name": "RegisteredAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "RegisteredBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "HasOrders", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeOrderCount", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeTotalSpent", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "format", "in": "query", "schema": {"type": "string", "default": "xlsx"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Customer/export-template": {"get": {"tags": ["Customer"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK"}}}}, "/api/Customer/validate-import": {"post": {"tags": ["Customer"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}}}}}}, "/api/Customer/import-statistics": {"post": {"tags": ["Customer"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CustomerImportStatisticsDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CustomerImportStatisticsDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerImportStatisticsDTO"}}}}}}}, "/api/Dashboard/summary": {"get": {"tags": ["Dashboard"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DashboardSummaryDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DashboardSummaryDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DashboardSummaryDTO"}}}}}}}, "/api/Dashboard/sales-trend": {"get": {"tags": ["Dashboard"], "parameters": [{"name": "period", "in": "query", "schema": {"type": "string", "default": "daily"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SalesTrendDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SalesTrendDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SalesTrendDTO"}}}}}}}, "/api/Dashboard/popular-products": {"get": {"tags": ["Dashboard"], "parameters": [{"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PopularProductDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PopularProductDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PopularProductDTO"}}}}}}}}, "/api/Dashboard/sales-by-category": {"get": {"tags": ["Dashboard"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategorySalesDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategorySalesDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategorySalesDTO"}}}}}}}}, "/api/Dashboard/order-status-distribution": {"get": {"tags": ["Dashboard"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderStatusDistributionDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderStatusDistributionDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderStatusDistributionDTO"}}}}}}}, "/api/FileManager/browse": {"get": {"tags": ["FileManager"], "parameters": [{"name": "Path", "in": "query", "schema": {"type": "string"}}, {"name": "Search", "in": "query", "schema": {"type": "string"}}, {"name": "FileType", "in": "query", "schema": {"type": "string"}}, {"name": "Extension", "in": "query", "schema": {"type": "string"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortOrder", "in": "query", "schema": {"type": "string"}}, {"name": "MinSize", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "MaxSize", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageNumber", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileBrowseResponseDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileBrowseResponseDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileBrowseResponseDTO"}}}}}}}, "/api/FileManager/folders": {"get": {"tags": ["FileManager"], "parameters": [{"name": "rootPath", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FolderStructureDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FolderStructureDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FolderStructureDTO"}}}}}}}, "/api/FileManager/info": {"get": {"tags": ["FileManager"], "parameters": [{"name": "filePath", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileItemDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileItemDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileItemDTO"}}}}}}}, "/api/FileManager/upload": {"post": {"tags": ["FileManager"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"FolderPath": {"type": "string"}, "CreateThumbnails": {"type": "boolean"}, "OverwriteExisting": {"type": "boolean"}, "files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"FolderPath": {"style": "form"}, "CreateThumbnails": {"style": "form"}, "OverwriteExisting": {"style": "form"}, "files": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileUploadResponseDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileUploadResponseDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadResponseDTO"}}}}}}}, "/api/FileManager/create-folder": {"post": {"tags": ["FileManager"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFolderRequestDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFolderRequestDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateFolderRequestDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileItemDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileItemDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileItemDTO"}}}}}}}, "/api/FileManager/delete": {"delete": {"tags": ["FileManager"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteFileRequestDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteFileRequestDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteFileRequestDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeleteFileResponseDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteFileResponseDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteFileResponseDTO"}}}}}}}, "/api/FileManager/move": {"post": {"tags": ["FileManager"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MoveFileRequestDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MoveFileRequestDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MoveFileRequestDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileOperationResponseDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileOperationResponseDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileOperationResponseDTO"}}}}}}}, "/api/FileManager/copy": {"post": {"tags": ["FileManager"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CopyFileRequestDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CopyFileRequestDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CopyFileRequestDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileOperationResponseDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileOperationResponseDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileOperationResponseDTO"}}}}}}}, "/api/FileManager/generate-thumbnail": {"post": {"tags": ["FileManager"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/FileManager/metadata": {"get": {"tags": ["FileManager"], "parameters": [{"name": "imagePath", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ImageMetadataDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ImageMetadataDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ImageMetadataDTO"}}}}}}}, "/api/FileManager/cleanup-orphaned": {"post": {"tags": ["FileManager"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/FileManager/sync-database": {"post": {"tags": ["FileManager"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/FileManager/missing-files": {"get": {"tags": ["FileManager"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/FileManager/health": {"get": {"tags": ["FileManager"], "responses": {"200": {"description": "OK"}}}}, "/api/FileManager/download": {"get": {"tags": ["FileManager"], "parameters": [{"name": "filePath", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/HealthCheck": {"get": {"tags": ["HealthCheck"], "responses": {"200": {"description": "OK"}}}}, "/api/Image/upload": {"post": {"tags": ["Image"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Files"], "type": "object", "properties": {"Files": {"type": "array", "items": {"type": "string", "format": "binary"}}, "folderName": {"type": "string"}}}, "encoding": {"Files": {"style": "form"}, "folderName": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ImageUploadResponseDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ImageUploadResponseDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ImageUploadResponseDTO"}}}}}}}, "/api/Image/system": {"get": {"tags": ["Image"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ImageUploadResponseDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ImageUploadResponseDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ImageUploadResponseDTO"}}}}}}}, "/api/Image/exists/{fileName}": {"get": {"tags": ["Image"], "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Image/{ids}": {"get": {"tags": ["Image"], "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ImageUploadResponseDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ImageUploadResponseDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ImageUploadResponseDTO"}}}}}}}, "/api/Order": {"get": {"tags": ["Order"], "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CustomerId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderStatus", "in": "query", "schema": {"type": "string"}}, {"name": "PaymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "MinAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "OrderDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ShippingCity", "in": "query", "schema": {"type": "string"}}, {"name": "ShippingState", "in": "query", "schema": {"type": "string"}}, {"name": "ShippingCountry", "in": "query", "schema": {"type": "string"}}, {"name": "IncludeDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderDTOPagedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderDTOPagedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderDTOPagedResult"}}}}}}, "post": {"tags": ["Order"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOrderDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderDTO"}}}}}}}, "/api/Order/all": {"get": {"tags": ["Order"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}}}}}}, "/api/Order/user/{userId}": {"get": {"tags": ["Order"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}}}}}}, "/api/Order/{id}": {"get": {"tags": ["Order"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderDTO"}}}}}}, "put": {"tags": ["Order"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOrderDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Order"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/{id}/status": {"put": {"tags": ["Order"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/bulk": {"delete": {"tags": ["Order"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkDeleteDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/recent": {"get": {"tags": ["Order"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}}}}}}, "/api/Order/status/{status}": {"get": {"tags": ["Order"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}}}}}}, "/api/Order/date-range": {"get": {"tags": ["Order"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}}}}}}}}, "/api/Order/revenue": {"get": {"tags": ["Order"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "number", "format": "double"}}, "application/json": {"schema": {"type": "number", "format": "double"}}, "text/json": {"schema": {"type": "number", "format": "double"}}}}}}}, "/api/Order/status-counts": {"get": {"tags": ["Order"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}}}}}}}, "/api/Order/import": {"post": {"tags": ["Order"], "parameters": [{"name": "validateOnly", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderExcelDTOExcelImportResultDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderExcelDTOExcelImportResultDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderExcelDTOExcelImportResultDTO"}}}}}}}, "/api/Order/export": {"get": {"tags": ["Order"], "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CustomerId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderStatus", "in": "query", "schema": {"type": "string"}}, {"name": "PaymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "MinAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "OrderDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ShippingCity", "in": "query", "schema": {"type": "string"}}, {"name": "ShippingState", "in": "query", "schema": {"type": "string"}}, {"name": "ShippingCountry", "in": "query", "schema": {"type": "string"}}, {"name": "IncludeDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "format", "in": "query", "schema": {"type": "string", "default": "xlsx"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/export-template": {"get": {"tags": ["Order"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/validate-import": {"post": {"tags": ["Order"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}}}}}}, "/api/Order/import-statistics": {"post": {"tags": ["Order"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderImportStatisticsDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderImportStatisticsDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderImportStatisticsDTO"}}}}}}}, "/api/Products": {"get": {"tags": ["Products"], "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "CategoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MinPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "IsFeatured", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreatedAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "StockQuantityMin", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StockQuantityMax", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MinRating", "in": "query", "schema": {"type": "number", "format": "float"}}, {"name": "SKU", "in": "query", "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDTOPagedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDTOPagedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDTOPagedResult"}}}}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDTO"}}}}}}}, "/api/Products/all": {"get": {"tags": ["Products"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}}}}}}, "/api/Products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDTO"}}}}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products/category/{categoryId}": {"get": {"tags": ["Products"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}}}}}}, "/api/Products/featured": {"get": {"tags": ["Products"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}}}}}}, "/api/Products/{id}/related": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}}}}}}, "/api/Products/top-rated": {"get": {"tags": ["Products"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}}}}}}, "/api/Products/low-stock": {"get": {"tags": ["Products"], "parameters": [{"name": "threshold", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}}}}}}}, "/api/Products/bulk": {"delete": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkDeleteDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Products/{id}/images": {"post": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"image": {"type": "string", "format": "binary"}}}, "encoding": {"image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Products/{productId}/images/{imageId}": {"delete": {"tags": ["Products"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "imageId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products/import": {"post": {"tags": ["Products"], "parameters": [{"name": "validateOnly", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductExcelDTOExcelImportResultDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductExcelDTOExcelImportResultDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductExcelDTOExcelImportResultDTO"}}}}}}}, "/api/Products/export": {"get": {"tags": ["Products"], "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "CategoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MinPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "IsFeatured", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreatedAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "StockQuantityMin", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StockQuantityMax", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MinRating", "in": "query", "schema": {"type": "number", "format": "float"}}, {"name": "SKU", "in": "query", "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "format", "in": "query", "schema": {"type": "string", "default": "xlsx"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products/export-template": {"get": {"tags": ["Products"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products/validate-import": {"post": {"tags": ["Products"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExcelValidationResultDTO"}}}}}}}, "/api/Products/import-statistics": {"post": {"tags": ["Products"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductImportStatisticsDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductImportStatisticsDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductImportStatisticsDTO"}}}}}}}, "/api/Review/product/{productId}": {"get": {"tags": ["Review"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReviewDTO"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReviewDTO"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReviewDTO"}}}}}}}}, "/api/Review/{id}": {"get": {"tags": ["Review"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ReviewDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ReviewDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReviewDTO"}}}}}}, "put": {"tags": ["Review"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateReviewDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateReviewDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateReviewDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Review"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Review/product/{productId}/rating": {"get": {"tags": ["Review"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "number", "format": "float"}}, "application/json": {"schema": {"type": "number", "format": "float"}}, "text/json": {"schema": {"type": "number", "format": "float"}}}}}}}, "/api/Review": {"post": {"tags": ["Review"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReviewDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateReviewDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateReviewDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Review"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Review"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Review"}}}}}}}}, "components": {"schemas": {"AddToCartDTO": {"required": ["productId", "quantity"], "type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "AuthResponseDTO": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "user": {"$ref": "#/components/schemas/UserDTO"}}, "additionalProperties": false}, "Banner": {"required": ["imageUrl", "title"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"maxLength": 100, "minLength": 0, "type": "string"}, "imageUrl": {"maxLength": 255, "minLength": 0, "type": "string"}, "link": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "displayOrder": {"type": "integer", "format": "int32"}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "BannerDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}, "link": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "displayOrder": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "BulkDeleteDTO": {"required": ["ids"], "type": "object", "properties": {"ids": {"minItems": 1, "type": "array", "items": {"type": "integer", "format": "int32"}}}, "additionalProperties": false}, "CartDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32", "nullable": true}, "sessionId": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "totalItems": {"type": "integer", "format": "int32"}, "updatedAt": {"type": "string", "format": "date-time"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CartItemDTO"}, "nullable": true}}, "additionalProperties": false}, "CartItemDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productSlug": {"type": "string", "nullable": true}, "productImage": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "unitPrice": {"type": "number", "format": "double"}, "subtotal": {"type": "number", "format": "double"}}, "additionalProperties": false}, "Category": {"required": ["name", "slug"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"maxLength": 100, "minLength": 0, "type": "string"}, "slug": {"maxLength": 100, "minLength": 0, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CategoryDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "parentName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "subcategories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}, "nullable": true}, "imageDetails": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDTO"}, "nullable": true}}, "additionalProperties": false}, "CategoryDTOPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}, "nullable": true}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata"}}, "additionalProperties": false}, "CategoryExcelDTO": {"required": ["name", "slug"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"maxLength": 100, "minLength": 2, "type": "string"}, "slug": {"maxLength": 100, "minLength": 0, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "parentName": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "productCount": {"type": "integer", "format": "int32"}, "subcategoryCount": {"type": "integer", "format": "int32"}, "level": {"type": "integer", "format": "int32"}, "categoryPath": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "totalRevenue": {"type": "number", "format": "double"}, "averageProductPrice": {"type": "number", "format": "double"}, "validationErrors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "rowNumber": {"type": "integer", "format": "int32"}, "hasErrors": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "CategoryExcelDTOExcelImportResultDTO": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryExcelDTO"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ExcelValidationErrorDTO"}, "nullable": true}, "totalRows": {"type": "integer", "format": "int32"}, "successfulRows": {"type": "integer", "format": "int32"}, "errorRows": {"type": "integer", "format": "int32"}, "isSuccess": {"type": "boolean", "readOnly": true}, "summary": {"type": "string", "nullable": true, "readOnly": true}, "processingTime": {"type": "string", "format": "date-span"}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "CategoryImportStatisticsDTO": {"type": "object", "properties": {"totalRows": {"type": "integer", "format": "int32"}, "newCategories": {"type": "integer", "format": "int32"}, "updatedCategories": {"type": "integer", "format": "int32"}, "errorRows": {"type": "integer", "format": "int32"}, "rootCategories": {"type": "integer", "format": "int32"}, "subcategories": {"type": "integer", "format": "int32"}, "maxHierarchyDepth": {"type": "integer", "format": "int32"}, "parentCategories": {"type": "array", "items": {"type": "string"}, "nullable": true}, "duplicateNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "circularReferences": {"type": "array", "items": {"type": "string"}, "nullable": true}, "estimatedProcessingTime": {"type": "string", "format": "date-span"}, "fileSizeBytes": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "CategorySalesDTO": {"type": "object", "properties": {"categoryId": {"type": "integer", "format": "int32"}, "categoryName": {"type": "string", "nullable": true}, "totalSales": {"type": "integer", "format": "int32"}, "totalRevenue": {"type": "number", "format": "double"}, "percentage": {"type": "number", "format": "double"}}, "additionalProperties": false}, "CopyFileRequestDTO": {"type": "object", "properties": {"sourcePaths": {"type": "array", "items": {"type": "string"}, "nullable": true}, "destinationPath": {"type": "string", "nullable": true}, "overwriteExisting": {"type": "boolean"}}, "additionalProperties": false}, "CreateCategoryDTO": {"required": ["name", "slug"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 2, "type": "string"}, "slug": {"maxLength": 100, "minLength": 0, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "imageIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "CreateCustomerDTO": {"required": ["email", "firstName", "lastName"], "type": "object", "properties": {"firstName": {"maxLength": 100, "minLength": 2, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 2, "type": "string"}, "email": {"maxLength": 100, "minLength": 0, "type": "string", "format": "email"}, "address": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "state": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "postalCode": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "country": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "phone": {"maxLength": 20, "minLength": 0, "type": "string", "format": "tel", "nullable": true}}, "additionalProperties": false}, "CreateFolderRequestDTO": {"type": "object", "properties": {"parentPath": {"type": "string", "nullable": true}, "folderName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateOrderDTO": {"required": ["orderItems", "paymentMethod", "shippingAddress", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "customerId": {"type": "integer", "format": "int32", "nullable": true}, "paymentMethod": {"maxLength": 50, "minLength": 0, "type": "string"}, "shippingAddress": {"maxLength": 255, "minLength": 0, "type": "string"}, "shippingCity": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "shippingState": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "shippingPostalCode": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "shippingCountry": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "contactPhone": {"maxLength": 100, "minLength": 0, "type": "string", "format": "tel", "nullable": true}, "contactEmail": {"maxLength": 100, "minLength": 0, "type": "string", "format": "email", "nullable": true}, "notes": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "orderItems": {"type": "array", "items": {"$ref": "#/components/schemas/CreateOrderItemDTO"}}}, "additionalProperties": false}, "CreateOrderItemDTO": {"required": ["productId", "quantity"], "type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateProductDTO": {"required": ["categoryId", "name", "price", "sku", "slug", "stockQuantity"], "type": "object", "properties": {"name": {"maxLength": 255, "minLength": 3, "type": "string"}, "slug": {"maxLength": 255, "minLength": 0, "type": "string"}, "description": {"type": "string", "nullable": true}, "price": {"minimum": 0.01, "type": "number", "format": "double"}, "originalPrice": {"type": "number", "format": "double"}, "stockQuantity": {"type": "integer", "format": "int32"}, "sku": {"maxLength": 50, "minLength": 0, "type": "string"}, "categoryId": {"type": "integer", "format": "int32"}, "isFeatured": {"type": "boolean"}, "isActive": {"type": "boolean"}, "imageIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "CreateReviewDTO": {"required": ["productId", "rating", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "rating": {"maximum": 5, "minimum": 1, "type": "integer", "format": "int32"}, "comment": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "Customer": {"required": ["email", "firstName", "lastName"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "firstName": {"maxLength": 100, "minLength": 0, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 0, "type": "string"}, "email": {"maxLength": 100, "minLength": 0, "type": "string", "format": "email"}, "address": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "state": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "postalCode": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "country": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "phone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "fullName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CustomerDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CustomerDTOPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDTO"}, "nullable": true}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata"}}, "additionalProperties": false}, "CustomerExcelDTO": {"required": ["email", "firstName", "lastName"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "firstName": {"maxLength": 100, "minLength": 2, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 2, "type": "string"}, "email": {"maxLength": 100, "minLength": 0, "type": "string", "format": "email"}, "address": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "state": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "postalCode": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "country": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "phone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "orderCount": {"type": "integer", "format": "int32"}, "totalSpent": {"type": "number", "format": "double"}, "averageOrderValue": {"type": "number", "format": "double"}, "lastOrderDate": {"type": "string", "format": "date-time", "nullable": true}, "daysSinceLastOrder": {"type": "integer", "format": "int32", "nullable": true}, "lifetimeValue": {"type": "number", "format": "double"}, "customerStatus": {"type": "string", "nullable": true}, "customerSegment": {"type": "string", "nullable": true}, "validationErrors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "rowNumber": {"type": "integer", "format": "int32"}, "hasErrors": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "CustomerExcelDTOExcelImportResultDTO": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerExcelDTO"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ExcelValidationErrorDTO"}, "nullable": true}, "totalRows": {"type": "integer", "format": "int32"}, "successfulRows": {"type": "integer", "format": "int32"}, "errorRows": {"type": "integer", "format": "int32"}, "isSuccess": {"type": "boolean", "readOnly": true}, "summary": {"type": "string", "nullable": true, "readOnly": true}, "processingTime": {"type": "string", "format": "date-span"}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "CustomerImportStatisticsDTO": {"type": "object", "properties": {"totalRows": {"type": "integer", "format": "int32"}, "newCustomers": {"type": "integer", "format": "int32"}, "updatedCustomers": {"type": "integer", "format": "int32"}, "errorRows": {"type": "integer", "format": "int32"}, "countries": {"type": "array", "items": {"type": "string"}, "nullable": true}, "states": {"type": "array", "items": {"type": "string"}, "nullable": true}, "cities": {"type": "array", "items": {"type": "string"}, "nullable": true}, "duplicateEmails": {"type": "array", "items": {"type": "string"}, "nullable": true}, "invalidEmails": {"type": "array", "items": {"type": "string"}, "nullable": true}, "invalidPhones": {"type": "array", "items": {"type": "string"}, "nullable": true}, "estimatedProcessingTime": {"type": "string", "format": "date-span"}, "fileSizeBytes": {"type": "integer", "format": "int64"}, "geographicDistribution": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DashboardSummaryDTO": {"type": "object", "properties": {"totalProducts": {"type": "integer", "format": "int32"}, "totalOrders": {"type": "integer", "format": "int32"}, "totalCustomers": {"type": "integer", "format": "int32"}, "totalRevenue": {"type": "number", "format": "double"}, "recentOrders": {"type": "array", "items": {"$ref": "#/components/schemas/RecentOrderDTO"}, "nullable": true}, "popularProducts": {"type": "array", "items": {"$ref": "#/components/schemas/PopularProductDTO"}, "nullable": true}, "salesByCategory": {"type": "array", "items": {"$ref": "#/components/schemas/CategorySalesDTO"}, "nullable": true}, "orderStatusDistribution": {"$ref": "#/components/schemas/OrderStatusDistributionDTO"}, "recentSalesTrend": {"type": "array", "items": {"$ref": "#/components/schemas/SalesTrendPointDTO"}, "nullable": true}}, "additionalProperties": false}, "DateRange": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "daysInRange": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "DeleteFileRequestDTO": {"type": "object", "properties": {"filePaths": {"type": "array", "items": {"type": "string"}, "nullable": true}, "permanent": {"type": "boolean"}}, "additionalProperties": false}, "DeleteFileResponseDTO": {"type": "object", "properties": {"deletedFiles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "successCount": {"type": "integer", "format": "int32"}, "errorCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ExcelErrorSeverity": {"enum": [1, 2, 3], "type": "integer", "format": "int32"}, "ExcelFileInfoDTO": {"type": "object", "properties": {"fileSizeBytes": {"type": "integer", "format": "int64"}, "worksheetCount": {"type": "integer", "format": "int32"}, "rowCount": {"type": "integer", "format": "int32"}, "columnCount": {"type": "integer", "format": "int32"}, "worksheetNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "fileFormat": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExcelValidationErrorDTO": {"type": "object", "properties": {"rowNumber": {"type": "integer", "format": "int32"}, "columnName": {"type": "string", "nullable": true}, "invalidValue": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "severity": {"$ref": "#/components/schemas/ExcelErrorSeverity"}, "propertyName": {"type": "string", "nullable": true}, "suggestedFix": {"type": "string", "nullable": true}, "context": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "ExcelValidationResultDTO": {"type": "object", "properties": {"isValid": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}, "fileInfo": {"$ref": "#/components/schemas/ExcelFileInfoDTO"}, "detectedColumns": {"type": "array", "items": {"type": "string"}, "nullable": true}, "missingColumns": {"type": "array", "items": {"type": "string"}, "nullable": true}, "extraColumns": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "FileBrowseResponseDTO": {"type": "object", "properties": {"currentPath": {"type": "string", "nullable": true}, "parentPath": {"type": "string", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/FileItemDTO"}, "nullable": true}, "totalItems": {"type": "integer", "format": "int32"}, "totalFiles": {"type": "integer", "format": "int32"}, "totalFolders": {"type": "integer", "format": "int32"}, "totalSize": {"type": "integer", "format": "int64"}, "formattedTotalSize": {"type": "string", "nullable": true}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}}, "additionalProperties": false}, "FileItemDTO": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "relativePath": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "size": {"type": "integer", "format": "int64"}, "formattedSize": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "modifiedAt": {"type": "string", "format": "date-time"}, "extension": {"type": "string", "nullable": true}, "thumbnailUrl": {"type": "string", "nullable": true}, "fullUrl": {"type": "string", "nullable": true}, "metadata": {"$ref": "#/components/schemas/ImageMetadataDTO"}, "isSelected": {"type": "boolean"}}, "additionalProperties": false}, "FileOperationResponseDTO": {"type": "object", "properties": {"processedFiles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "successCount": {"type": "integer", "format": "int32"}, "errorCount": {"type": "integer", "format": "int32"}, "operation": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileUploadResponseDTO": {"type": "object", "properties": {"uploadedFiles": {"type": "array", "items": {"$ref": "#/components/schemas/FileItemDTO"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "successCount": {"type": "integer", "format": "int32"}, "errorCount": {"type": "integer", "format": "int32"}, "totalSize": {"type": "integer", "format": "int64"}, "formattedTotalSize": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FolderStructureDTO": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "relativePath": {"type": "string", "nullable": true}, "fileCount": {"type": "integer", "format": "int32"}, "folderCount": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "totalSize": {"type": "integer", "format": "int64"}, "formattedSize": {"type": "string", "nullable": true}, "subfolders": {"type": "array", "items": {"$ref": "#/components/schemas/FolderStructureDTO"}, "nullable": true}, "isExpanded": {"type": "boolean"}, "hasChildren": {"type": "boolean"}}, "additionalProperties": false}, "ImageDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "fileName": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "altText": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ImageMetadataDTO": {"type": "object", "properties": {"width": {"type": "integer", "format": "int32"}, "height": {"type": "integer", "format": "int32"}, "format": {"type": "string", "nullable": true}, "aspectRatio": {"type": "number", "format": "double"}, "colorSpace": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ImageResponseDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "fileName": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "altText": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ImageUploadResponseDTO": {"type": "object", "properties": {"images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageResponseDTO"}, "nullable": true}}, "additionalProperties": false}, "LoginDTO": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "MoveFileRequestDTO": {"type": "object", "properties": {"sourcePaths": {"type": "array", "items": {"type": "string"}, "nullable": true}, "destinationPath": {"type": "string", "nullable": true}, "overwriteExisting": {"type": "boolean"}}, "additionalProperties": false}, "OrderDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "userFullName": {"type": "string", "nullable": true}, "customerId": {"type": "integer", "format": "int32", "nullable": true}, "customerFullName": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "orderStatus": {"type": "string", "nullable": true}, "paymentMethod": {"type": "string", "nullable": true}, "shippingAddress": {"type": "string", "nullable": true}, "shippingCity": {"type": "string", "nullable": true}, "shippingState": {"type": "string", "nullable": true}, "shippingPostalCode": {"type": "string", "nullable": true}, "shippingCountry": {"type": "string", "nullable": true}, "contactPhone": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "orderDate": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "orderItems": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItemDTO"}, "nullable": true}}, "additionalProperties": false}, "OrderDTOPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDTO"}, "nullable": true}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata"}}, "additionalProperties": false}, "OrderExcelDTO": {"required": ["orderStatus", "paymentMethod", "shippingAddress", "totalAmount"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "userId": {"type": "integer", "format": "int32", "nullable": true}, "userEmail": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "userFullName": {"type": "string", "nullable": true}, "customerId": {"type": "integer", "format": "int32", "nullable": true}, "customerEmail": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "customerFullName": {"type": "string", "nullable": true}, "totalAmount": {"minimum": 0.01, "type": "number", "format": "double"}, "orderStatus": {"maxLength": 50, "minLength": 0, "type": "string"}, "paymentMethod": {"maxLength": 50, "minLength": 0, "type": "string"}, "shippingAddress": {"maxLength": 255, "minLength": 0, "type": "string"}, "shippingCity": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "shippingState": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "shippingPostalCode": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "shippingCountry": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "contactPhone": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "contactEmail": {"maxLength": 100, "minLength": 0, "type": "string", "format": "email", "nullable": true}, "notes": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "orderDate": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "itemCount": {"type": "integer", "format": "int32"}, "orderItems": {"type": "string", "nullable": true}, "subtotal": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "shippingCost": {"type": "number", "format": "double"}, "discountAmount": {"type": "number", "format": "double"}, "daysSinceOrder": {"type": "integer", "format": "int32"}, "trackingNumber": {"type": "string", "nullable": true}, "validationErrors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "rowNumber": {"type": "integer", "format": "int32"}, "hasErrors": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "OrderExcelDTOExcelImportResultDTO": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/OrderExcelDTO"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ExcelValidationErrorDTO"}, "nullable": true}, "totalRows": {"type": "integer", "format": "int32"}, "successfulRows": {"type": "integer", "format": "int32"}, "errorRows": {"type": "integer", "format": "int32"}, "isSuccess": {"type": "boolean", "readOnly": true}, "summary": {"type": "string", "nullable": true, "readOnly": true}, "processingTime": {"type": "string", "format": "date-span"}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "OrderImportStatisticsDTO": {"type": "object", "properties": {"totalRows": {"type": "integer", "format": "int32"}, "newOrders": {"type": "integer", "format": "int32"}, "updatedOrders": {"type": "integer", "format": "int32"}, "errorRows": {"type": "integer", "format": "int32"}, "uniqueUsers": {"type": "array", "items": {"type": "string"}, "nullable": true}, "uniqueCustomers": {"type": "array", "items": {"type": "string"}, "nullable": true}, "uniqueProducts": {"type": "array", "items": {"type": "string"}, "nullable": true}, "statusDistribution": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "paymentMethodDistribution": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "totalOrderValue": {"type": "number", "format": "double"}, "averageOrderValue": {"type": "number", "format": "double"}, "orderDateRange": {"$ref": "#/components/schemas/DateRange"}, "invalidUserEmails": {"type": "array", "items": {"type": "string"}, "nullable": true}, "invalidCustomerEmails": {"type": "array", "items": {"type": "string"}, "nullable": true}, "invalidProductSkus": {"type": "array", "items": {"type": "string"}, "nullable": true}, "invalidOrderStatuses": {"type": "array", "items": {"type": "string"}, "nullable": true}, "calculationErrors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "estimatedProcessingTime": {"type": "string", "format": "date-span"}, "fileSizeBytes": {"type": "integer", "format": "int64"}, "relationshipWarnings": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "OrderItemDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "orderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productImageUrl": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "unitPrice": {"type": "number", "format": "double"}, "subtotal": {"type": "number", "format": "double"}}, "additionalProperties": false}, "OrderStatusDistributionDTO": {"type": "object", "properties": {"pending": {"type": "integer", "format": "int32"}, "processing": {"type": "integer", "format": "int32"}, "shipped": {"type": "integer", "format": "int32"}, "delivered": {"type": "integer", "format": "int32"}, "cancelled": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PaginationMetadata": {"type": "object", "properties": {"currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasNext": {"type": "boolean"}, "hasPrevious": {"type": "boolean"}, "nextPage": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "previousPage": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "PopularProductDTO": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "totalSold": {"type": "integer", "format": "int32"}, "totalRevenue": {"type": "number", "format": "double"}}, "additionalProperties": false}, "Product": {"required": ["name", "price", "sku", "slug", "stockQuantity"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"maxLength": 255, "minLength": 3, "type": "string"}, "slug": {"maxLength": 255, "minLength": 0, "type": "string"}, "description": {"type": "string", "nullable": true}, "price": {"minimum": 0.01, "type": "number", "format": "double"}, "originalPrice": {"type": "number", "format": "double"}, "stockQuantity": {"type": "integer", "format": "int32"}, "sku": {"maxLength": 50, "minLength": 0, "type": "string"}, "categoryId": {"type": "integer", "format": "int32"}, "isFeatured": {"type": "boolean"}, "isActive": {"type": "boolean"}, "averageRating": {"type": "number", "format": "float"}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ProductDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "originalPrice": {"type": "number", "format": "double"}, "stockQuantity": {"type": "integer", "format": "int32"}, "sku": {"type": "string", "nullable": true}, "categoryId": {"type": "integer", "format": "int32"}, "categoryName": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "isActive": {"type": "boolean"}, "averageRating": {"type": "number", "format": "float"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "images": {"type": "array", "items": {"type": "string"}, "nullable": true}, "imageDetails": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDTO"}, "nullable": true}}, "additionalProperties": false}, "ProductDTOPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}, "nullable": true}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata"}}, "additionalProperties": false}, "ProductExcelDTO": {"required": ["categoryId", "name", "price", "sku", "slug", "stockQuantity"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"maxLength": 255, "minLength": 3, "type": "string"}, "slug": {"maxLength": 255, "minLength": 0, "type": "string"}, "description": {"type": "string", "nullable": true}, "price": {"minimum": 0.01, "type": "number", "format": "double"}, "originalPrice": {"type": "number", "format": "double"}, "stockQuantity": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "sku": {"maxLength": 50, "minLength": 0, "type": "string"}, "categoryId": {"type": "integer", "format": "int32"}, "categoryName": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "isActive": {"type": "boolean"}, "averageRating": {"type": "number", "format": "float"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "imageUrls": {"type": "string", "nullable": true}, "reviewCount": {"type": "integer", "format": "int32"}, "totalSales": {"type": "integer", "format": "int32"}, "revenue": {"type": "number", "format": "double"}, "lastSaleDate": {"type": "string", "format": "date-time", "nullable": true}, "validationErrors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "rowNumber": {"type": "integer", "format": "int32"}, "hasErrors": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ProductExcelDTOExcelImportResultDTO": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductExcelDTO"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ExcelValidationErrorDTO"}, "nullable": true}, "totalRows": {"type": "integer", "format": "int32"}, "successfulRows": {"type": "integer", "format": "int32"}, "errorRows": {"type": "integer", "format": "int32"}, "isSuccess": {"type": "boolean", "readOnly": true}, "summary": {"type": "string", "nullable": true, "readOnly": true}, "processingTime": {"type": "string", "format": "date-span"}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "ProductImportStatisticsDTO": {"type": "object", "properties": {"totalRows": {"type": "integer", "format": "int32"}, "newProducts": {"type": "integer", "format": "int32"}, "updatedProducts": {"type": "integer", "format": "int32"}, "errorRows": {"type": "integer", "format": "int32"}, "categories": {"type": "array", "items": {"type": "string"}, "nullable": true}, "duplicateSkus": {"type": "array", "items": {"type": "string"}, "nullable": true}, "estimatedProcessingTime": {"type": "string", "format": "date-span"}, "fileSizeBytes": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "RecentOrderDTO": {"type": "object", "properties": {"orderId": {"type": "integer", "format": "int32"}, "orderDate": {"type": "string", "format": "date-time"}, "customerName": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "orderStatus": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterDTO": {"required": ["email", "password", "username"], "type": "object", "properties": {"username": {"maxLength": 50, "minLength": 3, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"maxLength": 100, "minLength": 6, "type": "string"}, "confirmPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Review": {"required": ["rating"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "rating": {"maximum": 5, "minimum": 1, "type": "integer", "format": "int32"}, "comment": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "user": {"$ref": "#/components/schemas/User"}, "product": {"$ref": "#/components/schemas/Product"}}, "additionalProperties": false}, "ReviewDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "userName": {"type": "string", "nullable": true}, "productId": {"type": "integer", "format": "int32"}, "rating": {"type": "integer", "format": "int32"}, "comment": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SalesTrendDTO": {"type": "object", "properties": {"period": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SalesTrendPointDTO"}, "nullable": true}}, "additionalProperties": false}, "SalesTrendPointDTO": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time"}, "revenue": {"type": "number", "format": "double"}, "orderCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateCartItemDTO": {"required": ["quantity"], "type": "object", "properties": {"quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateCategoryDTO": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 2, "type": "string"}, "slug": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "imageIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "UpdateCustomerDTO": {"required": ["firstName", "lastName"], "type": "object", "properties": {"firstName": {"maxLength": 100, "minLength": 2, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 2, "type": "string"}, "address": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "state": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "postalCode": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "country": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "phone": {"maxLength": 20, "minLength": 0, "type": "string", "format": "tel", "nullable": true}}, "additionalProperties": false}, "UpdateOrderDTO": {"type": "object", "properties": {"customerId": {"type": "integer", "format": "int32", "nullable": true}, "paymentMethod": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "shippingAddress": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "shippingCity": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "shippingState": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "shippingPostalCode": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "shippingCountry": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "contactPhone": {"maxLength": 100, "minLength": 0, "type": "string", "format": "tel", "nullable": true}, "contactEmail": {"maxLength": 100, "minLength": 0, "type": "string", "format": "email", "nullable": true}, "notes": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateOrderStatusDTO": {"required": ["orderStatus"], "type": "object", "properties": {"orderStatus": {"maxLength": 50, "minLength": 0, "type": "string"}}, "additionalProperties": false}, "UpdateProductDTO": {"required": ["name", "price"], "type": "object", "properties": {"name": {"maxLength": 255, "minLength": 3, "type": "string"}, "slug": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "price": {"minimum": 0.01, "type": "number", "format": "double"}, "originalPrice": {"type": "number", "format": "double"}, "stockQuantity": {"type": "integer", "format": "int32"}, "sku": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "categoryId": {"type": "integer", "format": "int32"}, "isFeatured": {"type": "boolean"}, "isActive": {"type": "boolean"}, "imageIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "UpdateReviewDTO": {"required": ["rating"], "type": "object", "properties": {"rating": {"maximum": 5, "minimum": 1, "type": "integer", "format": "int32"}, "comment": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "User": {"required": ["email", "passwordHash", "role", "username"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "username": {"maxLength": 50, "minLength": 0, "type": "string"}, "email": {"maxLength": 100, "minLength": 0, "type": "string", "format": "email"}, "passwordHash": {"maxLength": 255, "minLength": 0, "type": "string"}, "fullName": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "address": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "phone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "role": {"maxLength": 20, "minLength": 0, "type": "string"}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}